import { getMetrics } from '../controllers/metrics.js';

export default async function metricsRoutes(fastify, options) {
  fastify.get('/metrics', {
    schema: {
      description: 'Get application metrics and statistics',
      tags: ['monitoring'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            uptime: {
              type: 'object',
              properties: {
                seconds: { type: 'number' },
                human: { type: 'string' }
              }
            },
            memory: {
              type: 'object',
              properties: {
                rss: { type: 'number' },
                heapTotal: { type: 'number' },
                heapUsed: { type: 'number' },
                external: { type: 'number' }
              }
            },
            cache: {
              type: 'object',
              properties: {
                totalArticles: { type: 'number' },
                lastUpdated: { type: ['string', 'null'] },
                hasError: { type: 'boolean' },
                errorMessage: { type: ['string', 'null'] },
                cacheSize: { type: 'string' },
                timeSinceLastUpdate: {
                  type: ['object', 'null'],
                  properties: {
                    seconds: { type: 'number' },
                    human: { type: 'string' }
                  }
                }
              }
            },
            statistics: {
              type: 'object',
              properties: {
                categoriesDistribution: { type: 'object' },
                totalCategories: { type: 'number' },
                recentArticles: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      title: { type: 'string' },
                      date_published: { type: 'string' },
                      tags: { type: 'array', items: { type: 'string' } }
                    }
                  }
                }
              }
            },
            version: { type: 'string' }
          }
        }
      }
    }
  }, getMetrics);
}
