import { withErrorHandling } from '../utils/errorHandler.js';

const getMetricsHandler = async (request, reply) => {
  // Accessing cacheManager decorated on the fastify instance
  const cacheManager = request.server.cacheManager;

  if (!cacheManager) {
    return reply.status(500).send({ error: "Cache manager not configured." });
  }

  // Get process information
  const memoryUsage = process.memoryUsage();
  const uptime = process.uptime();

  // Get cache statistics from CacheManager
  const cacheStats = cacheManager.getStats();
  const memoryStats = cacheManager.getMemoryUsage();
  const aggregatedFeed = cacheManager.getAggregatedFeed();

  // Get the most recent articles info from aggregated feed
  const recentArticles = aggregatedFeed.items.slice(0, 5).map(item => ({
    id: item.id,
    title: item.title,
    date_published: item.date_published,
    tags: item.tags
  }));

  // Calculate time since last update (most recent across all categories)
  let timeSinceLastUpdate = null;
  if (aggregatedFeed.lastUpdated) {
    const lastUpdateTime = new Date(aggregatedFeed.lastUpdated);
    const now = new Date();
    timeSinceLastUpdate = {
      seconds: Math.floor((now - lastUpdateTime) / 1000),
      human: formatTimeDifference(now - lastUpdateTime)
    };
  }

  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: {
      seconds: Math.floor(uptime),
      human: formatUptime(uptime)
    },
    memory: {
      process: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100, // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100, // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100, // MB
        external: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100 // MB
      },
      cache: {
        totalMB: memoryStats.totalMB,
        totalBytes: memoryStats.totalBytes,
        categories: memoryStats.categories
      }
    },
    cache: {
      type: 'category-based',
      totalArticles: cacheStats.totalArticles,
      totalCategories: Object.keys(cacheStats.categories).length,
      availableCategories: cacheManager.getAvailableCategories(),
      timeSinceLastUpdate,
      categories: cacheStats.categories,
      limits: cacheStats.limits
    },
    statistics: {
      recentArticles,
      categoryBreakdown: Object.entries(cacheStats.categories).map(([category, stats]) => ({
        category,
        count: stats.count,
        utilizationPercent: stats.utilizationPercent,
        lastUpdated: stats.lastUpdated
      }))
    },
    version: '1.2.0'
  };
};

// Helper function to format uptime in human-readable format
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);
  
  return parts.join(' ');
}

// Helper function to format time difference in human-readable format
function formatTimeDifference(milliseconds) {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
}

export const getMetrics = withErrorHandling(getMetricsHandler);
