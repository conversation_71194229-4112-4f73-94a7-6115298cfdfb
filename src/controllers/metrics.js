import { withErrorHandling } from '../utils/errorHandler.js';

const getMetricsHandler = async (request, reply) => {
  // Accessing cachedFeedData decorated on the fastify instance
  const cachedFeedData = request.server.cachedFeedData;
  
  // Get process information
  const memoryUsage = process.memoryUsage();
  const uptime = process.uptime();
  
  // Calculate cache statistics
  const cacheStats = {
    totalArticles: cachedFeedData?.items?.length || 0,
    lastUpdated: cachedFeedData?.lastUpdated,
    cacheSize: cachedFeedData?.items ? 
      JSON.stringify(cachedFeedData.items).length : 0
  };

  // Get the most recent articles info
  const recentArticles = cachedFeedData?.items?.slice(0, 5).map(item => ({
    id: item.id,
    title: item.title,
    date_published: item.date_published,
    tags: item.tags
  })) || [];

  // Calculate categories distribution
  const categoriesStats = {};
  if (cachedFeedData?.items) {
    cachedFeedData.items.forEach(item => {
      if (item.tags && item.tags.length > 0) {
        const category = item.tags[0];
        categoriesStats[category] = (categoriesStats[category] || 0) + 1;
      }
    });
  }

  // Calculate time since last update
  let timeSinceLastUpdate = null;
  if (cachedFeedData?.lastUpdated) {
    const lastUpdateTime = new Date(cachedFeedData.lastUpdated);
    const now = new Date();
    timeSinceLastUpdate = {
      seconds: Math.floor((now - lastUpdateTime) / 1000),
      human: formatTimeDifference(now - lastUpdateTime)
    };
  }

  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: {
      seconds: Math.floor(uptime),
      human: formatUptime(uptime)
    },
    memory: {
      rss: Math.round(memoryUsage.rss / 1024 / 1024 * 100) / 100, // MB
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024 * 100) / 100, // MB
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024 * 100) / 100, // MB
      external: Math.round(memoryUsage.external / 1024 / 1024 * 100) / 100 // MB
    },
    cache: {
      ...cacheStats,
      cacheSize: `${Math.round(cacheStats.cacheSize / 1024 * 100) / 100} KB`,
      timeSinceLastUpdate
    },
    statistics: {
      categoriesDistribution: categoriesStats,
      totalCategories: Object.keys(categoriesStats).length,
      recentArticles
    },
    version: '1.0.0'
  };
};

// Helper function to format uptime in human-readable format
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);
  
  return parts.join(' ');
}

// Helper function to format time difference in human-readable format
function formatTimeDifference(milliseconds) {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
}

export const getMetrics = withErrorHandling(getMetricsHandler);
