// This controller will need access to cachedFeedData,
// which will be provided by app.js when registering the route.

import { withErrorHandling } from '../utils/errorHandler.js';
import { ValidationError } from '../utils/errors.js';

const getFeedHandler = async (request, reply) => {
  // Accessing cachedFeedData decorated on the fastify instance
  const originalFeedData = request.server.cachedFeedData;
  const { category: requestedCategory } = request.params; // Extract category from request.params object

  if (!originalFeedData) {
    // This case should ideally not happen if app.js correctly provides it
    return reply.status(500).send({ error: "Feed data source not configured." });
  }

  if (originalFeedData.items.length === 0 && !originalFeedData.lastUpdated) {
    return reply.status(503).send({
      message: "Feed data is not yet available. Please try again later.",
      details: "Initial scrape might be in progress or has not run yet."
    });
  }

  if (requestedCategory !== undefined) {
    // Validate category parameter
    if (typeof requestedCategory !== 'string' || requestedCategory.trim() === '') {
      throw new ValidationError('Invalid category parameter: category cannot be empty', { category: requestedCategory });
    }

    // Filter by the requested category tag
    // The tags now use simple names (e.g., 'english' instead of 'en/english')
    const filteredItems = originalFeedData.items.filter(item =>
      item.tags && item.tags.length > 0 && item.tags[0] === requestedCategory
    );

    // Optional: Update feed title with the category
    const categoryNameForTitle = requestedCategory.charAt(0).toUpperCase() + requestedCategory.slice(1);
    const newTitle = `${originalFeedData.title || 'MTI Feed'} - ${categoryNameForTitle}`; // Example: "MTI Feed - Kozelet"

    return {
      ...originalFeedData, // Keep other feed metadata (e.g., link, description, language)
      title: newTitle, // If we also want to update the title
      items: filteredItems // List of filtered articles
    };
  } else {
    // If there is no category parameter, return the full feed
    return originalFeedData;
  }
};

export const getFeed = withErrorHandling(getFeedHandler);
