# MTI.hu News JSON Feed API

## Project Overview

This application is designed for automatically collecting news articles from the MTI.hu website and serving them in JSON Feed format. The application runs in a Docker container, optimized for resource-efficient and fast operation.

> **⚠️ Important Disclaimer**: This project is intended for **educational and personal use only**. Please respect MTI.hu's terms of service and robots.txt file. Users are responsible for ensuring their usage complies with all applicable laws and website policies. Consider reaching out to MTI.hu for permission if you plan to use this for commercial purposes.

## Functional Requirements

#### Performance optimization

### Scraping optimization
- **Single-threaded sequential** processing
- **Random delays** (1-10 seconds)
- Connection reuse (single browser instance)
- Request rate limiting
- Selective element loading

### Human-like behavior
- Natural timing between page loads
- Session cookie preservation
- User-Agent rotation (optional)
- Mouse movement simulation (optional)

### 1. Login and authentication
- Automatic login to the MTI.hu website
- URL: `https://mti.hu/bejelentkezes`
- Process:
  1. Click "Regisztrált látogatóként" button
  2. Enter email and password
  3. Session management for subsequent requests

### 2. News collection
**Sources (categories):**
- https://mti.hu/kozelet
- https://mti.hu/gazdasag  
- https://mti.hu/vilag
- https://mti.hu/kultura
- https://mti.hu/sport
- https://mti.hu/en/english

**Collection process:**
1. Visit each category page
2. Extract news links from div tags
3. Collect image links (if available)
4. Fetch individual news content

### 3. News content processing
**News structure:**
- **Timestamp:** publication time in span tag
- **Title:** h1 tag content
- **Lead:** first paragraph
- **Content:** additional paragraphs (h2, em highlights)
- **Image:** from collection page + internal description
- **Tags:** tags found in separate div

**Output format:**
- According to JSON Feed standard
- Clean but structured text
- Image and description at the beginning of output
- Sorted by time (newest first)

### 4. Technical requirements
- Runnable in Docker container
- Fast and resource-efficient operation
- RESTful API endpoints
- Error handling and logging
- **Single-threaded operation** for human-like behavior
- **Random pauses** (1-10 seconds) between pages

## Versioning and Releases

This project follows [Semantic Versioning](https://semver.org/) and uses [Conventional Commits](https://www.conventionalcommits.org/) for automated changelog generation and versioning.

### Commit Message Format

Please use the following format for commit messages:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `build`: Changes that affect the build system or external dependencies
- `ci`: Changes to our CI configuration files and scripts
- `chore`: Other changes that don't modify src or test files

**Examples:**
```bash
feat: add support for video content extraction
fix: resolve authentication timeout issues
docs: update API documentation with new endpoints
```

### Release Process

Releases are automated through GitHub Actions:

1. **Automatic releases**: Triggered on push to main branch when commits contain `feat:`, `fix:`, or `BREAKING CHANGE`
2. **Manual releases**: Can be triggered via GitHub Actions workflow dispatch

**Release commands:**
```bash
# Dry run to see what would be released
npm run release:dry-run

# Create a patch release (1.0.0 -> 1.0.1)
npm run release:patch

# Create a minor release (1.0.0 -> 1.1.0)
npm run release:minor

# Create a major release (1.0.0 -> 2.0.0)
npm run release:major

# Automatic release based on conventional commits
npm run release
```

### Changelog

All notable changes are documented in [CHANGELOG.md](./CHANGELOG.md). The changelog is automatically updated during the release process based on conventional commit messages.

### Development Setup for Contributors

To ensure commit message compliance during development, install the commit message hook:

```bash
# Install the commit message validation hook
cp .githooks/commit-msg .git/hooks/commit-msg && chmod +x .git/hooks/commit-msg
```

This will validate your commit messages locally before they are committed.

## Getting Started

### Prerequisites
- Node.js 18+
- NPM or Yarn
- Valid MTI.hu account credentials

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url> # Replace <repository-url> with the actual URL
cd mti-feed
npm install --include=dev

# cleanep: rm -rf node_modules && rm -f package-lock.json && npm cache clean --force
```

2. **Set up environment variables:**
Create a `.env` file in the root of the project:
```bash
cp .env.sample .env
# OR create .env manually
nano .env # Or your preferred editor
```

Example `.env` file:
```env
MTI_EMAIL=<EMAIL>
MTI_PASSWORD=your-secure-password
NODE_ENV=development
LOG_LEVEL=info
PORT=3000
```
**Important:** Ensure `.env` is listed in your `.gitignore` file.

### Using Authentication in Code

```javascript
import authService from './src/services/auth.js';

// Login and maintain session
await authService.maintainSession();

// Check if authenticated
if (authService.isAuthenticated) {
  const page = authService.getPage();
  // Use page for scraping
}

// Cleanup
await authService.cleanup();
```

### Security Features

- ✅ **Secure credential storage** - Environment variables only
- ✅ **Session management** - Automatic session validation
- ✅ **Human-like behavior** - Random delays, realistic browser settings
- ✅ **Error handling** - Comprehensive error types and logging
- ✅ **Resource cleanup** - Proper browser lifecycle management

### Testing
```bash
# Run all tests (127 tests across 11 files)
npm run test:run

# Run with coverage report (78.75% coverage)
npm run test:coverage

# Interactive test UI
npm run test:ui

# Run specific test suites
npx vitest run tests/unit/auth.test.js
npx vitest run tests/unit/embeddedContent.test.js
npx vitest run tests/integration/metrics.test.js
```

## Technology Stack

### Backend
- **Node.js** - Runtime environment
- **Fastify** - Modern, fast web framework
- **Playwright** - Web scraping and automation (with JavaScript support)
- **node-cron** - Scheduled tasks

### Infrastructure
- **Docker** - Containerization
- **Alpine Linux** - Lightweight base image

## Application Architecture

### Modules

#### 1. Authentication Service (`src/services/auth.js`)
Functions:
- login(email, password)
- maintainSession()
- isAuthenticated()

#### 2. Scraper Service (`src/services/scraper.js`)
Functions:
- scrapeCategories()
- scrapeArticleList(categoryUrl)
- scrapeArticleContent(articleUrl)
- extractImageInfo(articleHtml)
- randomDelay(min, max)
- maintainHumanBehavior()

#### 3. Feed Controllers (`src/controllers/feed.js`, `src/controllers/health.js`, `src/controllers/metrics.js`)
- Handles incoming API requests for news feeds, health checks, and metrics.
- Maps requests to appropriate services.

#### 4. API Routes (`src/routes/`)
- Defines the API endpoints (e.g., /feed, /health, /metrics) and associates them with controller functions.
- Uses Fastify for routing.

#### 5. Utility Modules (`src/utils/`)
- `config.js`: Manages application configuration.
- `env.js`: Handles environment variable loading and validation.
- `errors.js`: Defines custom error classes.
- `helpers.js`: Provides helper functions.
- `logger.js`: Configures and provides logging functionality with tracing support.
- `tracing.js`: Provides trace ID generation and operation timing capabilities.
- `embeddedContent.js`: Processes and extracts embedded content like videos and social media posts.

### File structure
```
mti-feed/
├── config/
│   └── default.json
├── data/
│   └── feedCache.json  # Example, content might vary
├── scripts/
│   └── test.js         # Example test script
├── src/
│   ├── app.js          # Main application entry point
│   ├── controllers/    # Request handlers
│   │   ├── feed.js
│   │   ├── health.js
│   │   └── metrics.js
│   ├── routes/         # API route definitions
│   │   ├── feed.js
│   │   ├── health.js
│   │   └── metrics.js
│   ├── services/       # Business logic (authentication, scraping)
│   │   ├── auth.js
│   │   └── scraper.js
│   └── utils/          # Utility modules (config, logger, etc.)
│       ├── config.js
│       ├── env.js
│       ├── errors.js
│       ├── helpers.js
│       ├── logger.js
│       ├── tracing.js
│       └── embeddedContent.js
├── tests/              # Test files
│   ├── fixtures/
│   │   └── html/
│   ├── integration/
│   ├── unit/
│   ├── basic.test.js
│   ├── setup.js
│   └── simple.test.js
├── .env                # Local environment variables (MUST be in .gitignore)
├── .gitignore
├── docker-compose.yml
├── Dockerfile
├── package.json
├── README.md
├── TESTING.md          # Details about testing strategies
├── vitest.config.js    # Vitest configuration
└── vitest.config.minimal.js # Minimal Vitest configuration
```

## API Endpoints

### News Feed Endpoints

#### GET /feed
**Description:** All news in JSON Feed format
**Response:**
```json
{
  "version": "https://jsonfeed.org/version/1.1",
  "title": "MTI News",
  "home_page_url": "https://mti.hu",
  "feed_url": "http://localhost:3000/feed",
  "items": [
    {
      "id": "unique-article-id",
      "title": "News Title",
      "content_html": "<p>Full content in HTML</p>",
      "content_text": "Clean text content",
      "url": "https://mti.hu/original-article-url",
      "date_published": "2025-06-04T10:30:00Z",
      "date_modified": "2025-06-04T10:30:00Z",
      "author": {
        "name": "MTI"
      },
      "tags": ["politics", "economy"],
      "image": "https://mti.hu/image-url.jpg",
      "summary": "Lead text",
      "_mti": {
        "category": "kozelet",
        "image_description": "Image description"
      }
    }
  ]
}
```

#### GET /feed/:category
**Description:** News from specific category
**Parameters:** `category` (kozelet, gazdasag, vilag, kultura, sport, english)

#### GET /health
**Description:** Service status check
**Response:**
```json
{
  "status": "healthy",
  "last_update": "2025-06-04T10:30:00Z",
  "articles_count": 150,
  "cache_status": "active"
}
```

#### GET /metrics
**Description:** System performance and cache statistics
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-05T10:30:00Z",
  "uptime": 3600,
  "memory": {
    "rss": 45.2,
    "heapTotal": 25.6,
    "heapUsed": 18.3,
    "external": 2.1
  },
  "cache": {
    "articles": 150,
    "lastUpdated": "2025-06-05T09:30:00Z",
    "cacheSize": "2.4MB"
  },
  "system": {
    "nodeVersion": "18.17.0",
    "platform": "linux",
    "cpuUsage": 15.2
  }
}
```

## Docker Configuration

> For detailed Docker setup instructions, refer to the `Dockerfile` and `docker-compose.yml`.

### Quick Start

1. Create a `.env` file (you can copy `.env.example` as a starting point) and add your MTI credentials:
   ```bash
   # Example .env file content
   MTI_EMAIL=<EMAIL>
   MTI_PASSWORD=your-secure-password
   NODE_ENV=production
   LOG_LEVEL=info
   # Add other necessary variables like PORT
   ```
   Ensure your `.env` file is included in your `.gitignore` to prevent committing credentials.

2. Run with Docker Compose:
   ```bash
   docker-compose up --build -d
   ```

3. Access the API at http://localhost:8080/feed

### Key Features

- **Data Persistence**: The `data` folder is mounted as a volume to preserve cache between restarts
- **Environment Variables**: MTI credentials and configuration via `.env` file
- **Automatic Restart**: Container restarts unless manually stopped
- **Cache Loading**: Previous articles are automatically loaded on startup

### docker-compose.yml
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:3000"
    volumes:
      - ./data:/usr/src/app/data  # Persistent data storage
    env_file:
      - .env
    restart: unless-stopped
```

## Configuration

The application uses a centralized configuration system that supports environment variable overrides for all settings. This allows for easy deployment customization without modifying configuration files.

### Environment Variables

All configuration options can be overridden using environment variables. Copy `.env.example` to `.env` and modify as needed:

```bash
# Required credentials
MTI_EMAIL=<EMAIL>
MTI_PASSWORD=your-password

# Server configuration (optional)
PORT=3000                    # Server port (default: 3000)
HOST=0.0.0.0                # Server host (default: 0.0.0.0)
TIMEZONE=UTC                # Server timezone (default: UTC)

# MTI service configuration (optional)
MTI_BASE_URL=https://mti.hu                    # MTI base URL
MTI_LOGIN_URL=https://mti.hu/bejelentkezes     # MTI login URL

# MTI timeout configuration (optional, in milliseconds)
MTI_TIMEOUT_PAGE_LOAD=60000      # Page load timeout (default: 60000)
MTI_TIMEOUT_NETWORK_IDLE=60000   # Network idle timeout (default: 60000)
MTI_TIMEOUT_SELECTOR=60000       # Selector wait timeout (default: 60000)
MTI_TIMEOUT_FORM_SUBMIT=60000    # Form submission timeout (default: 60000)
MTI_TIMEOUT_AUTH_CHECK=60000     # Authentication check timeout (default: 60000)

# Scraping configuration (optional)
SCRAPE_INTERVAL="0 * * * *"      # Cron expression for scraping schedule
SCRAPE_TIMEZONE=Europe/Budapest  # Timezone for scheduling (default: Europe/Budapest)
SCRAPE_TIMEOUT=30000            # General scraping timeout (default: 30000)
SCRAPE_RETRIES=3                # Number of retry attempts (default: 3)
SCRAPE_HUMAN_DELAY_MIN=1000     # Minimum human-like delay (default: 1000)
SCRAPE_HUMAN_DELAY_MAX=10000    # Maximum human-like delay (default: 10000)
SCRAPE_JITTER_MAX=600           # Maximum random jitter in seconds (default: 600)
SCRAPE_CONTENT_READY_TIMEOUT=10000  # Content ready timeout (default: 10000)

# Cache configuration (optional)
MAX_NEW_ARTICLES_PER_RUN=25          # Maximum new articles to fetch per scraping run (default: 25)

# Feed metadata configuration (optional)
FEED_TITLE="MTI News"                        # Feed title (default: MTI News)
FEED_HOME_PAGE_URL=https://mti.hu            # Feed home page URL (default: https://mti.hu)
FEED_ICON=https://static.mti.hu/logo.svg     # Feed icon URL (default: https://static.mti.hu/logo.svg)

# Logging configuration (optional)
LOG_LEVEL=info                               # Log level: silent, error, warn, info, debug, trace
```

### config/default.json

The base configuration file contains all default settings. Environment variables override these values:

```json
{
  "server": {
    "port": 3000,
    "host": "0.0.0.0",
    "timezone": "UTC"
  },
  "mti": {
    "baseUrl": "https://mti.hu",
    "loginUrl": "https://mti.hu/bejelentkezes",
    "timeouts": {
      "pageLoad": 60000,
      "networkIdle": 60000,
      "selector": 60000,
      "formSubmit": 60000,
      "authCheck": 60000
    },
    "categories": [
      "kozelet",
      "gazdasag",
      "vilag",
      "kultura",
      "sport",
      "en/english"
    ],
    "categoryContainerSelectors": {
      "kozelet": "section.flex > div:nth-child(1) > div:nth-child(3)",
      "en/english": "section.flex:nth-child(3) > div:nth-child(1) > div:nth-child(1)",
      "default": "section.flex > div:nth-child(1) > div:nth-child(2)"
    }
  },
  "scraping": {
    "interval": "0 6-23/2,0 * * *",
    "timezone": "Europe/Budapest",
    "timeout": 30000,
    "retries": 3,
    "humanDelay": {
      "min": 1000,
      "max": 10000
    },
    "jitterSeconds": {
      "max": 600
    },
    "contentReadyTimeout": 10000,
    "maxNewArticlesPerRun": 25
  },
  "feed": {
    "version": "https://jsonfeed.org/version/1.1",
    "title": "MTI News",
    "homePageUrl": "https://mti.hu",
    "icon": "https://static.mti.hu/logo.svg"
  },
  "logging": {
    "level": "info"
  }
}
```

### Configuration Priority

Configuration values are resolved in the following order (highest to lowest priority):

1. **Environment variables** - Override any setting
2. **config/default.json** - Base configuration file
3. **Built-in defaults** - Fallback values in the code

This allows for flexible deployment configurations while maintaining sensible defaults.

### Scheduling System

Uses a single cron expression for consistent intervals throughout the day.

**Configuration:** Use the `interval` property:
```json
"interval": "0 * * * *"  // Every hour
```

#### Cron Expression Format
All scheduling uses standard cron syntax:
```
* * * * * *
│ │ │ │ │ │
│ │ │ │ │ └─ Day of Week (0-7, 0=Sunday)
│ │ │ │ └── Month (1-12)
│ │ │ └───── Day of Month (1-31)
│ │ └──────── Hour (0-23)
│ └─────────── Minute (0-59)
└──────────── Second (0-59, optional)
```

#### Jitter Mechanism
Both scheduling modes include jitter to avoid predictable scraping patterns:
- Random delay of 0-600 seconds (10 minutes max) added to each scheduled execution
- Helps distribute load and appear more human-like
- Configurable via `jitterSeconds.max` in configuration

#### Timezone Configuration
The scheduling system respects timezone settings for accurate time-based scheduling:

**Priority Order:**
1. `scraping.timezone` - Scraping-specific timezone setting
2. `server.timezone` - Global server timezone setting  
3. Default fallback: `"UTC"`

**Configuration:**
```json
"scraping": {
  "timezone": "Europe/Budapest",
  // ... other settings
}
```

**Important Notes:**
- Business hours (9-17) are interpreted in the configured timezone
- All cron expressions use the same timezone for consistency
- Docker containers should set system timezone or use configuration
- Valid timezone values: IANA timezone identifiers (e.g., `"Europe/Budapest"`, `"UTC"`, `"America/New_York"`)

## Error Handling and Logging

### Error types
- **AuthenticationError:** Login error
- **ScrapingError:** Data fetching error  
- **ParsingError:** HTML processing error
- **NetworkError:** Network connection error

### Logging levels
- **error:** Critical errors
- **warn:** Warnings
- **info:** General information
- **debug:** Development details

### Centralized Tracing and Monitoring
The application implements comprehensive tracing and monitoring capabilities:

#### Trace ID Management
- **Unique Trace IDs** - Generated using crypto.randomBytes for each operation
- **Operation Tracking** - All external calls (authentication, scraping, API requests) are traced
- **Nested Operations** - Child operations inherit parent trace IDs for complete request flows
- **Duration Measurement** - Automatic timing of operations for performance monitoring

#### Enhanced Logging
All logs now include structured tracing information:
```json
{
  "traceId": "3be272594b964ec2",
  "operation": "auth.initBrowser",
  "duration": 125,
  "timestamp": "2025-06-09T19:51:57.432Z",
  "level": "info",
  "msg": "Browser initialized successfully"
}
```

#### Operation Types Traced
- **Authentication Operations** - Browser initialization, login flows, session management
- **Scraping Operations** - Category navigation, content extraction, article processing  
- **Scheduled Tasks** - Cron job execution, cache updates
- **API Requests** - HTTP calls to external services

#### Usage Examples
```javascript
// Create a tracer for an operation
const tracer = createTracer('auth.login', existingTraceId);

// Log with trace context
logWithTrace('info', tracer, 'Login successful', { 
  hasSessionCookie: true 
});

// Create child operations
const navTracer = tracer.createChild('navigate');
logWithTrace('debug', navTracer, 'Navigated to login page');
```

## Performance Optimization

### Scraping optimization
- **Single-threaded sequential** category processing
- Connection pooling
- Request rate limiting
- Selective element loading

### Cache strategy
- **Category-based in-memory caching**: Separate cache instances for each news category (kozelet, gazdasag, vilag, kultura, sport, english).
- **No persistent file storage**: Cache is initialized empty on application start.
- **Independent category management**: Each category can be queried and updated independently.
- **Size limits**: Per-category size limits prevent memory issues in long-running applications.
- **Controlled growth**: New articles per scraping run limited by `maxNewArticlesPerRun` parameter (default: 25).
- **Aggregated views**: Full feed endpoint combines all categories, category-specific endpoints serve individual caches.

### Docker optimization
- Multi-stage build
- Alpine Linux base
- Minimal dependencies
- Health checks

### Content Processing Optimization
- **Embedded Content Processing** - Efficient YouTube, Facebook, Twitter, Instagram embed extraction
- **HTML Sanitization** - Clean content processing with selective element parsing
- **Image Optimization** - Smart image URL extraction and validation
- **Text Normalization** - Advanced text cleaning and formatting

### Monitoring and Metrics
- **Performance Metrics** - Real-time system monitoring via `/metrics` endpoint
- **Memory Usage Tracking** - Heap and RSS memory monitoring
- **Cache Statistics** - Cache size, hit rates, and update frequency tracking
- **Uptime Monitoring** - System availability and health metrics
- **Distributed Tracing** - Complete request flow tracking with unique trace IDs
- **Operation Timing** - Duration measurement for all external operations
- **Structured Logging** - Rich context logging with trace information

## Current Project Status

### ✅ Implemented Features
- **Authentication System** - Secure MTI.hu login with session managemenț
- **Multi-Category Scraping** - Support for all MTI categories (kozelet, gazdasag, vilag, kultura, sport, english)
- **JSON Feed 1.1 Compliance** - Standards-compliant feed generation
- **Content Processing** - HTML cleaning, text normalization, and image extraction
- **Embedded Content Support** - YouTube, Facebook, Twitter, Instagram embed processing
- **System Metrics** - Performance monitoring and cache statistics via `/metrics` endpoint
- **Health Monitoring** - Service status and availability checking via `/health` endpoint
- **Caching System** - Optimized content caching with icon field support
- **Error Handling** - Comprehensive error management and logging
- **Centralized Tracing** - Distributed tracing with unique trace IDs and operation timing
- **Docker Support** - Containerized deployment with Alpine Linux

### 📊 Technical Metrics
- **Test Coverage:** 78.75% statements, 78.94% branches, 87.27% functions
- **Test Suite:** 127 tests across 11 test files
- **Performance:** ~10s test execution time
- **Dependencies:** Modern stack with Fastify 5.3.3, Vitest 3.2.1, Playwright 1.45.0
- **Architecture:** Clean separation of concerns with controllers, services, and utilities

### 🔧 Recent Enhancements
- Enhanced embedded content processing capabilities
- New system metrics endpoint for monitoring
- Improved cache structure with icon field support
- **Centralized Logging and Tracing** - Complete request tracing with duration measurement
- **Operation Monitoring** - Trace ID propagation across all external operations
- Expanded test coverage with comprehensive integration tests
- Optimized performance and resource usage

### 🚀 Ready for Production
This MTI Feed API is production-ready with:
- Comprehensive test coverage
- Robust error handling
- Performance monitoring
- Docker containerization
- Standards-compliant JSON Feed output
- Human-like scraping behavior to respect source website

## Support and Maintenance

For issues, feature requests, or contributions, please refer to the test suite (`npm run test:coverage`) to understand the current functionality and ensure any changes maintain the established quality standards.

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for information about:
- Code hygiene and quality standards
- Development workflow
- Automated checks and pre-commit hooks
- Review guidelines

We maintain strict standards for code cleanliness, including automated checks for TODO/FIXME comments and other code quality measures.
