import { describe, it, expect, beforeAll, afterAll, vi, beforeEach } from 'vitest';
import { buildApp } from '../../src/app.js';

// Mock services to avoid timeouts during testing
vi.mock('../../src/services/auth.js', () => {
  return {
    default: class MockAuthService {
      constructor() {
        this.isAuthenticated = true;
        this.browserInitialized = true;
      }
      initBrowser = vi.fn().mockResolvedValue(undefined);
      maintainSession = vi.fn().mockResolvedValue(true);
      checkAuthStatus = vi.fn().mockResolvedValue(true);
      getPage = vi.fn().mockReturnValue({});
      cleanup = vi.fn().mockResolvedValue(undefined);
      isBrowserInitialized = vi.fn().mockReturnValue(true);
      login = vi.fn().mockResolvedValue(true);
    }
  };
});

vi.mock('../../src/services/scraper.js', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    default: class MockScraperService {
      constructor(authService, config, logger) {
        this.logger = logger || console;
      }
      scrapeCategories = vi.fn().mockResolvedValue([]);
    }
  };
});

describe('Error Handling Integration', () => {
  let app;

  beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    app = await buildApp({ isTest: true });
    await app.ready();
  });

  beforeEach(async () => {
    // Reset cache state for clean tests
    if (app.cachedFeedData) {
      app.cachedFeedData.items = [];
      app.cachedFeedData.lastUpdated = null;
    }
  });

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  it('should handle errors in feed controller gracefully', async () => {
    // Force an error by corrupting the cached data temporarily
    const originalData = app.cachedFeedData;
    app.cachedFeedData = null;

    const response = await app.inject({
      method: 'GET',
      url: '/feed'
    });

    expect(response.statusCode).toBe(500);
    expect(response.json()).toEqual({
      error: "Feed data source not configured."
    });

    // Restore original data
    app.cachedFeedData = originalData;
  });

  it('should handle invalid category parameter', async () => {
    // Ensure cached data is properly set first
    app.cachedFeedData = {
      items: [{
        id: 'test-item',
        title: 'Test Item',
        tags: ['test']
      }],
      lastUpdated: new Date().toISOString()
    };

    // Test with a whitespace-only category which should trigger validation error
    const response = await app.inject({
      method: 'GET',
      url: '/feed/   ' // Category with only whitespace
    });

    expect(response.statusCode).toBe(400);
    const responseBody = response.json();
    expect(responseBody.error).toBe('Request failed');
    expect(responseBody.code).toBe('VALIDATION_ERROR');
    expect(responseBody.message).toBe('Invalid category parameter: category cannot be empty');
  });

  it('should return proper health status', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/health'
    });

    expect(response.statusCode).toBe(200);
    const responseBody = response.json();
    expect(responseBody.status).toBe('healthy');
    expect(responseBody.timestamp).toBeDefined();
    expect(responseBody.cache).toBeDefined();
  });

  it('should return metrics without errors', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/metrics'
    });

    expect(response.statusCode).toBe(200);
    const responseBody = response.json();
    expect(responseBody.status).toBe('ok');
    expect(responseBody.timestamp).toBeDefined();
    expect(responseBody.uptime).toBeDefined();
    expect(responseBody.memory).toBeDefined();
  });

  it('should handle 404 errors properly', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/nonexistent'
    });

    expect(response.statusCode).toBe(404);
  });
});