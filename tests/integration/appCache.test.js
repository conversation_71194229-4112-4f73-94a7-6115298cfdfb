import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { buildApp } from '../../src/app.js';
import configUtil from '../../src/utils/config.js';

// Mock configUtil
vi.mock('../../src/utils/config.js', () => ({
  default: {
    server: {
      port: 3000, host: '0.0.0.0', timezone: 'UTC',
    },
    mti: {
      baseUrl: "https://mti.hu", loginUrl: "https://mti.hu/bejelentkezes",
      categories: ["kozelet", "gazdasag"], categoryContainerSelectors: { default: "selector" }
    },
    scraping: {
      interval: '0 * * * *', timeout: 30000, retries: 3,
      humanDelay: { min: 1, max: 2 }, jitterSeconds: { max: 1 },
      contentReadyTimeout: 10000, maxNewArticlesPerRun: 5,
    },
    feed: {
      version: 'https://jsonfeed.org/version/1.1',
      title: 'MTI News',
      homePageUrl: 'https://mti.hu',
      icon: 'https://static.mti.hu/logo.svg'
    },
    logging: { level: 'silent' },
    get: function(key) {
      const keys = key.split('.');
      let current = this.default;
      for (const k of keys) {
        if (current && typeof current === 'object' && k in current) {
          current = current[k];
        } else { return undefined; }
      }
      return current;
    }
  }
}));

// Mock ScraperService - Global scope for hoisting
const { mockScrapedArticles } = vi.hoisted(() => ({
  mockScrapedArticles: vi.fn()
}));
vi.mock('../../src/services/scraper.js', () => ({
  default: vi.fn().mockImplementation(() => ({
    scrapeCategories: mockScrapedArticles,
  })),
}));

// Mock AuthService - Global scope for hoisting
vi.mock('../../src/services/auth.js', () => ({
  default: vi.fn().mockImplementation(() => ({
    initBrowser: vi.fn().mockResolvedValue(undefined),
    maintainSession: vi.fn().mockResolvedValue(undefined),
    isBrowserInitialized: vi.fn().mockReturnValue(true),
    isAuthenticated: true,
    getPage: vi.fn().mockReturnValue({}), // Basic mock page
    cleanup: vi.fn().mockResolvedValue(undefined),
  })),
}));

describe('App Cache Logic - In-Memory Cache Initialization', () => {
  const defaultCacheStructure = {
    version: 'https://jsonfeed.org/version/1.1',
    title: 'MTI News',
    home_page_url: 'https://mti.hu',
    feed_url: `http://localhost:3000/feed`,
    icon: 'https://static.mti.hu/logo.svg',
    items: [],
    lastUpdated: null,
  };

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default in-memory cache structure', async () => {
    const app = await buildApp({ test: true });
    expect(app.cachedFeedData).toEqual(defaultCacheStructure);
    await app.close();
  });

  it('should allow cache data to be updated in memory', async () => {
    const app = await buildApp({ test: true });
    const updatedCacheData = {
      ...app.cachedFeedData,
      items: [{ id: 'test-id', url: 'http://example.com/test', title: 'Test Article' }],
      lastUpdated: new Date().toISOString(),
    };
    app.cachedFeedData = updatedCacheData;

    expect(app.cachedFeedData.items).toHaveLength(1);
    expect(app.cachedFeedData.items[0].title).toBe('Test Article');
    await app.close();
  });

  it('should maintain cache structure after app close (no file persistence)', async () => {
    const app = await buildApp({ test: true });
    app.cachedFeedData.items = [{ id: 'test-id', url: 'http://example.com/test', title: 'Test Article' }];

    // Close app - should not attempt any file operations
    await app.close();

    // Cache data should have been in memory only
    expect(app.cachedFeedData.items).toHaveLength(1);
  });
});

describe('App Cache Logic - runScraperAndUpdateCacheInternal', () => {
  let app;

  beforeEach(async () => {
    mockScrapedArticles.mockReset();
    app = await buildApp({ test: true });
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
    vi.clearAllMocks();
  });

  it('should add new articles to an empty cache', async () => {
    const articlesFromScraper = [
      { url: 'http://example.com/1', title: 'Article 1', date_published: new Date(Date.now() - 10000).toISOString(), tags: ['cat1'] },
      { url: 'http://example.com/2', title: 'Article 2', date_published: new Date(Date.now() - 20000).toISOString(), tags: ['cat2'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);
    expect(app.cachedFeedData.items).toHaveLength(0);
    await app.runScraperAndUpdateCache();
    expect(mockScrapedArticles).toHaveBeenCalledWith(expect.any(Set), null, configUtil.scraping.maxNewArticlesPerRun);
    expect(app.cachedFeedData.items).toHaveLength(2);
    expect(app.cachedFeedData.items[0].url).toBe('http://example.com/1');
    expect(app.cachedFeedData.lastUpdated).not.toBeNull();
  });

  it('should add new articles to existing cache and sort by date', async () => {
    app.cachedFeedData.items = [
      { url: 'http://example.com/old1', title: 'Old Article 1', date_published: new Date(Date.now() - 300000).toISOString(), tags: ['cat_old'] },
      { url: 'http://example.com/old2', title: 'Old Article 2', date_published: new Date(Date.now() - 400000).toISOString(), tags: ['cat_old'] },
    ];
    const articlesFromScraper = [
      { url: 'http://example.com/new1', title: 'New Article 1', date_published: new Date(Date.now() - 10000).toISOString(), tags: ['cat_new'] },
      { url: 'http://example.com/new2', title: 'New Article 2', date_published: new Date(Date.now() - 20000).toISOString(), tags: ['cat_new'] },
      { url: 'http://example.com/new3', title: 'New Article 3', date_published: new Date(Date.now() - 5000).toISOString(), tags: ['cat_new'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);
    await app.runScraperAndUpdateCache();
    expect(app.cachedFeedData.items).toHaveLength(5); // 2 old + 3 new
    expect(app.cachedFeedData.items[0].url).toBe('http://example.com/new3'); // Newest first
    expect(app.cachedFeedData.items[4].url).toBe('http://example.com/old2'); // Oldest last
    const expectedExistingUrls = new Set(['http://example.com/old1', 'http://example.com/old2']);
    expect(mockScrapedArticles).toHaveBeenCalledWith(expectedExistingUrls, null, configUtil.scraping.maxNewArticlesPerRun);
  });

  it('should not add duplicate articles if scraper somehow returns an existing URL', async () => {
    app.cachedFeedData.items = [
      { url: 'http://example.com/existing', title: 'Existing Article', date_published: new Date().toISOString(), tags: ['cat_exist'] },
    ];
    const articlesFromScraper = [
      { url: 'http://example.com/existing', title: 'Existing Article Updated?', date_published: new Date(Date.now() + 1000).toISOString(), tags: ['cat_exist_updated'] },
      { url: 'http://example.com/newUnique', title: 'New Unique Article', date_published: new Date().toISOString(), tags: ['cat_new_unique'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);
    await app.runScraperAndUpdateCache();
    expect(app.cachedFeedData.items).toHaveLength(2);
    const existingArticleInCache = app.cachedFeedData.items.find(item => item.url === 'http://example.com/existing');
    expect(existingArticleInCache.title).toBe('Existing Article Updated?'); 
  });
  
  it('should handle empty array from scraper without errors', async () => {
    mockScrapedArticles.mockResolvedValue([]);
    app.cachedFeedData.items = [
      { url: 'http://example.com/1', title: 'Article 1', date_published: new Date().toISOString(), tags: ['cat1'] }
    ];
    const initialItems = [...app.cachedFeedData.items];
    await app.runScraperAndUpdateCache();
    expect(app.cachedFeedData.items).toEqual(initialItems);
  });

  it('should handle scraper error and log it', async () => {
    const errorMessage = 'Scraper failed spectacularly';
    mockScrapedArticles.mockRejectedValue(new Error(errorMessage));
    await app.runScraperAndUpdateCache();
    // Should update timestamp even on error
    expect(app.cachedFeedData.lastUpdated).not.toBeNull();
  });
});
