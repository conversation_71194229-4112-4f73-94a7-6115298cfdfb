import * as fs from 'fs';
import path from 'path';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { buildApp } from '../../src/app.js';
import configUtil from '../../src/utils/config.js';

// Mock fs module completely
vi.mock('fs', () => {
  const mockFunctions = {
    existsSync: vi.fn(),
    readFileSync: vi.fn(),
    writeFileSync: vi.fn(),
    mkdirSync: vi.fn(),
  };
  
  return {
    default: mockFunctions,
    ...mockFunctions,
  };
});

// Mock configUtil
vi.mock('../../src/utils/config.js', () => ({
  default: {
    server: {
      port: 3000, host: '0.0.0.0', timezone: 'UTC',
    },
    mti: {
      baseUrl: "https://mti.hu", loginUrl: "https://mti.hu/bejelentkezes",
      categories: ["kozelet", "gazdasag"], categoryContainerSelectors: { default: "selector" }
    },
    scraping: {
      interval: '0 * * * *', timeout: 30000, retries: 3,
      humanDelay: { min: 1, max: 2 }, jitterSeconds: { max: 1 },
      contentReadyTimeout: 10000, maxArticlesToProcess: 10,
      cacheFilePath: 'data/testFeedCache.json', cacheMaxSize: 5,
    },
    feed: {
      version: 'https://jsonfeed.org/version/1.1',
      title: 'MTI News',
      homePageUrl: 'https://mti.hu',
      icon: 'https://static.mti.hu/logo.svg'
    },
    logging: { level: 'silent' },
    get: function(key) {
      const keys = key.split('.');
      let current = this.default;
      for (const k of keys) {
        if (current && typeof current === 'object' && k in current) {
          current = current[k];
        } else { return undefined; }
      }
      return current;
    }
  }
}));

// Mock ScraperService - Global scope for hoisting
const { mockScrapedArticles } = vi.hoisted(() => ({
  mockScrapedArticles: vi.fn()
}));
vi.mock('../../src/services/scraper.js', () => ({
  default: vi.fn().mockImplementation(() => ({
    scrapeCategories: mockScrapedArticles,
  })),
}));

// Mock AuthService - Global scope for hoisting
vi.mock('../../src/services/auth.js', () => ({
  default: vi.fn().mockImplementation(() => ({
    initBrowser: vi.fn().mockResolvedValue(undefined),
    maintainSession: vi.fn().mockResolvedValue(undefined),
    isBrowserInitialized: vi.fn().mockReturnValue(true),
    isAuthenticated: true,
    getPage: vi.fn().mockReturnValue({}), // Basic mock page
    cleanup: vi.fn().mockResolvedValue(undefined),
  })),
}));

describe('App Cache Logic - buildApp Initialization and Shutdown', () => {
  const testCacheFilePath = path.resolve(configUtil.scraping.cacheFilePath);
  const defaultCacheStructure = {
    version: 'https://jsonfeed.org/version/1.1',
    title: 'MTI News',
    home_page_url: 'https://mti.hu',
    feed_url: `http://localhost:3000/feed`,
    icon: 'https://static.mti.hu/logo.svg',
    items: [],
    lastUpdated: null,
  };

  beforeEach(() => {
    fs.existsSync.mockReset();
    fs.readFileSync.mockReset();
    fs.writeFileSync.mockReset();
    fs.mkdirSync.mockReset();
    fs.existsSync.mockReturnValue(false);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default cache if cache file does not exist', async () => {
    fs.existsSync.mockReturnValue(false);
    const app = await buildApp({ test: true });
    expect(fs.existsSync).toHaveBeenCalledWith(testCacheFilePath);
    expect(fs.readFileSync).not.toHaveBeenCalled();
    expect(app.cachedFeedData).toEqual(defaultCacheStructure);
    await app.close();
  });

  it('should load cache from file if it exists and is valid', async () => {
    const mockCacheData = {
      ...defaultCacheStructure,
      items: [{ id: 'test-id', url: 'http://example.com/test', title: 'Test Article' }],
      lastUpdated: new Date().toISOString(),
    };
    fs.existsSync.mockReturnValue(true);
    fs.readFileSync.mockReturnValue(JSON.stringify(mockCacheData));
    const app = await buildApp({ test: true });
    expect(fs.existsSync).toHaveBeenCalledWith(testCacheFilePath);
    expect(fs.readFileSync).toHaveBeenCalledWith(testCacheFilePath, 'utf-8');
    expect(app.cachedFeedData).toEqual(mockCacheData);
    await app.close();
  });

  it('should initialize with default cache if cache file exists but is invalid JSON', async () => {
    fs.existsSync.mockReturnValue(true);
    fs.readFileSync.mockReturnValue('invalid json');
    const app = await buildApp({ test: true });
    expect(fs.existsSync).toHaveBeenCalledWith(testCacheFilePath);
    expect(fs.readFileSync).toHaveBeenCalledWith(testCacheFilePath, 'utf-8');
    expect(app.cachedFeedData).toEqual(defaultCacheStructure);
    await app.close();
  });
  
  it('should initialize with default cache if cache file exists but has invalid structure', async () => {
    const invalidStructureCacheData = { title: 'MTI News' };
    fs.existsSync.mockReturnValue(true);
    fs.readFileSync.mockReturnValue(JSON.stringify(invalidStructureCacheData));
    const app = await buildApp({ test: true });
    expect(app.cachedFeedData.items).toEqual([]);
    expect(app.cachedFeedData.title).toEqual(defaultCacheStructure.title);
    await app.close();
  });

  it('should save cache to file on app close', async () => {
    fs.existsSync.mockReturnValue(false);
    const app = await buildApp({ test: true });
    const updatedCacheData = {
      ...app.cachedFeedData,
      items: [{ id: 'save-test', url: 'http://example.com/save', title: 'Save Test Article' }],
      lastUpdated: new Date().toISOString(),
    };
    app.cachedFeedData = updatedCacheData;
    await app.close();
    expect(fs.writeFileSync).toHaveBeenCalledTimes(1);
    expect(fs.writeFileSync).toHaveBeenCalledWith(
      testCacheFilePath,
      JSON.stringify(updatedCacheData, null, 2)
    );
  });

  it('should create cache directory if it does not exist when saving', async () => {
    fs.existsSync.mockImplementation((p) => p !== path.dirname(testCacheFilePath) && p !== testCacheFilePath);
    const app = await buildApp({ test: true });
    await app.close();
    expect(fs.mkdirSync).toHaveBeenCalledWith(path.dirname(testCacheFilePath), { recursive: true });
    expect(fs.writeFileSync).toHaveBeenCalled();
  });

  it('should not attempt to create cache directory if it already exists when saving', async () => {
    fs.existsSync.mockImplementation((p) => p === path.dirname(testCacheFilePath) || p === testCacheFilePath); // Directory exists, file doesn't initially
    fs.existsSync.mockImplementation((p) => {
        if (p === path.dirname(testCacheFilePath)) return true; // Directory exists
        return false; // File does not
    });
    const app = await buildApp({ test: true });
    await app.close();
    expect(fs.mkdirSync).not.toHaveBeenCalled();
    expect(fs.writeFileSync).toHaveBeenCalled();
  });
});

describe('App Cache Logic - runScraperAndUpdateCacheInternal', () => {
  let app;

  beforeEach(async () => {
    fs.existsSync.mockReset().mockReturnValue(false);
    fs.readFileSync.mockReset();
    fs.writeFileSync.mockReset();
    fs.mkdirSync.mockReset();
    mockScrapedArticles.mockReset();
    app = await buildApp({ test: true });
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
    vi.clearAllMocks();
  });

  it('should add new articles to an empty cache and respect maxSize', async () => {
    const articlesFromScraper = [
      { url: 'http://example.com/1', title: 'Article 1', date_published: new Date(Date.now() - 10000).toISOString(), tags: ['cat1'] },
      { url: 'http://example.com/2', title: 'Article 2', date_published: new Date(Date.now() - 20000).toISOString(), tags: ['cat2'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);
    expect(app.cachedFeedData.items).toHaveLength(0);
    await app.runScraperAndUpdateCache();
    expect(mockScrapedArticles).toHaveBeenCalledWith(expect.any(Set), null, configUtil.scraping.maxArticlesToProcess);
    expect(app.cachedFeedData.items).toHaveLength(2);
    expect(app.cachedFeedData.items[0].url).toBe('http://example.com/1');
    expect(app.cachedFeedData.lastUpdated).not.toBeNull();
  });

  it('should add new articles to existing cache, sort, and apply maxSize', async () => {
    app.cachedFeedData.items = [
      { url: 'http://example.com/old1', title: 'Old Article 1', date_published: new Date(Date.now() - 300000).toISOString(), tags: ['cat_old'] },
      { url: 'http://example.com/old2', title: 'Old Article 2', date_published: new Date(Date.now() - 400000).toISOString(), tags: ['cat_old'] },
    ];
    const articlesFromScraper = [
      { url: 'http://example.com/new1', title: 'New Article 1', date_published: new Date(Date.now() - 10000).toISOString(), tags: ['cat_new'] },
      { url: 'http://example.com/new2', title: 'New Article 2', date_published: new Date(Date.now() - 20000).toISOString(), tags: ['cat_new'] },
      { url: 'http://example.com/new3', title: 'New Article 3', date_published: new Date(Date.now() - 5000).toISOString(), tags: ['cat_new'] },
      { url: 'http://example.com/new4', title: 'New Article 4', date_published: new Date(Date.now() - 30000).toISOString(), tags: ['cat_new'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);
    await app.runScraperAndUpdateCache();
    const expectedCacheSize = configUtil.scraping.cacheMaxSize;
    expect(app.cachedFeedData.items).toHaveLength(expectedCacheSize);
    expect(app.cachedFeedData.items[0].url).toBe('http://example.com/new3');
    expect(app.cachedFeedData.items[4].url).toBe('http://example.com/old1');
    const expectedExistingUrls = new Set(['http://example.com/old1', 'http://example.com/old2']);
    expect(mockScrapedArticles).toHaveBeenCalledWith(expectedExistingUrls, null, configUtil.scraping.maxArticlesToProcess);
  });

  it('should not add duplicate articles if scraper somehow returns an existing URL', async () => {
    app.cachedFeedData.items = [
      { url: 'http://example.com/existing', title: 'Existing Article', date_published: new Date().toISOString(), tags: ['cat_exist'] },
    ];
    const articlesFromScraper = [
      { url: 'http://example.com/existing', title: 'Existing Article Updated?', date_published: new Date(Date.now() + 1000).toISOString(), tags: ['cat_exist_updated'] },
      { url: 'http://example.com/newUnique', title: 'New Unique Article', date_published: new Date().toISOString(), tags: ['cat_new_unique'] },
    ];
    mockScrapedArticles.mockResolvedValue(articlesFromScraper);
    await app.runScraperAndUpdateCache();
    expect(app.cachedFeedData.items).toHaveLength(2);
    const existingArticleInCache = app.cachedFeedData.items.find(item => item.url === 'http://example.com/existing');
    expect(existingArticleInCache.title).toBe('Existing Article Updated?'); 
  });
  
  it('should handle empty array from scraper without errors', async () => {
    mockScrapedArticles.mockResolvedValue([]);
    app.cachedFeedData.items = [
      { url: 'http://example.com/1', title: 'Article 1', date_published: new Date().toISOString(), tags: ['cat1'] }
    ];
    const initialItems = [...app.cachedFeedData.items];
    await app.runScraperAndUpdateCache();
    expect(app.cachedFeedData.items).toEqual(initialItems);
  });

  it('should handle scraper error and log it', async () => {
    const errorMessage = 'Scraper failed spectacularly';
    mockScrapedArticles.mockRejectedValue(new Error(errorMessage));
    await app.runScraperAndUpdateCache();
    // Should update timestamp even on error
    expect(app.cachedFeedData.lastUpdated).not.toBeNull();
  });
});
