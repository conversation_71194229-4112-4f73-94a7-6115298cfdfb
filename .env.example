# MTI.hu Authentication Credentials - REQUIRED
# Copy this to .env and fill in your actual credentials
MTI_EMAIL=<EMAIL>
MTI_PASSWORD=your-password

# Server Configuration (Optional)
#PORT=3000
#HOST=0.0.0.0
#TIMEZONE=UTC

# MTI Configuration (Optional)
#MTI_BASE_URL=https://mti.hu
#MTI_LOGIN_URL=https://mti.hu/bejelentkezes

# MTI Timeout Configuration (Optional, in milliseconds)
#MTI_TIMEOUT_PAGE_LOAD=60000
#MTI_TIMEOUT_NETWORK_IDLE=60000
#MTI_TIMEOUT_SELECTOR=60000
#MTI_TIMEOUT_FORM_SUBMIT=60000
#MTI_TIMEOUT_AUTH_CHECK=60000

# Scraping Configuration (Optional)
#SCRAPE_INTERVAL=0 6-23/2,0 * * *
#SCRAPE_TIMEZONE=Europe/Budapest
#SCRAPE_TIMEOUT=30000
#SCRAPE_RETRIES=3
#SCRAPE_HUMAN_DELAY_MIN=1000
#SCRAPE_HUMAN_DELAY_MAX=10000
#SCRAPE_JITTER_MAX=600
#SCRAPE_CONTENT_READY_TIMEOUT=10000

# Cache Configuration (Optional)
#MAX_NEW_ARTICLES_PER_RUN=25

# Feed Metadata Configuration (Optional)
#FEED_TITLE=MTI News
#FEED_HOME_PAGE_URL=https://mti.hu
#FEED_ICON=https://static.mti.hu/logo.svg

# Logging Configuration (Optional)
#LOG_LEVEL=info

# Instructions:
# 1. Copy this file to .env
# 2. Replace the sample values with your actual MTI.hu credentials
# 3. Uncomment and modify any optional settings as needed
# 4. The .env file is automatically ignored by git to protect your credentials
